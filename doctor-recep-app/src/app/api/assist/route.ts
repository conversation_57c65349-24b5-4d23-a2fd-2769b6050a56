/**
 * CELER ASSIST API ENDPOINT
 * Frontend-only AI enhancement tool using Groq via Vercel AI SDK
 * 
 * Provides contextual suggestions for editing consultation JSON fields
 * Built on existing enterprise architecture without backend changes
 */

import { NextRequest, NextResponse } from 'next/server'
import { createGroq } from '@ai-sdk/groq'
import { generateObject } from 'ai'
import { z } from 'zod'
import { verifySession } from '@/lib/auth/supabase-helpers'

// Initialize Groq with API key
const groq = createGroq({
  apiKey: process.env.GROQ_API_KEY
})

// Suggestion schema for structured AI responses
const suggestionSchema = z.object({
  suggestions: z.array(z.object({
    id: z.string().describe('Unique identifier for this suggestion'),
    title: z.string().describe('Short, actionable title for the suggestion'),
    description: z.string().describe('Brief explanation of what this suggestion does'),
    newValue: z.string().describe('The improved content for this field'),
    confidence: z.number().min(0).max(1).describe('Confidence score between 0 and 1')
  })).max(3).describe('Array of 2-3 helpful suggestions'),
  reasoning: z.string().describe('Brief explanation of why these suggestions were made')
})

// Context interface for AI requests
interface AssistContext {
  fieldName: string
  currentValue: string
  fullConsultation: any
  patientAge?: number
  consultationType?: string
  customRequest?: string
}

export async function POST(request: NextRequest) {
  try {
    // Verify user session using existing auth system
    const session = await verifySession()
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Parse request body
    const body = await request.json()
    const { fieldName, currentValue, fullConsultation, customRequest } = body as AssistContext

    // Validate required fields
    if (!fieldName || currentValue === undefined || !fullConsultation) {
      return NextResponse.json(
        { error: 'Missing required fields: fieldName, currentValue, fullConsultation' },
        { status: 400 }
      )
    }

    // Build context for AI
    const context = buildAssistContext(body)

    // Generate correlation ID for tracking (use existing enterprise pattern)
    const correlationId = `assist_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

    console.log(`🤖 [${correlationId}] AI assist request for field: ${fieldName}`)

    // Sanitize data before sending to Groq (basic privacy protection)
    const sanitizedConsultation = sanitizeForGroq(fullConsultation)

    // Build prompt for Groq
    const prompt = buildPrompt(fieldName, currentValue, sanitizedConsultation, customRequest)

    // Call Groq via Vercel AI SDK
    const startTime = Date.now()
    const { object } = await generateObject({
      model: groq('llama-3.1-8b-instant'),
      schema: suggestionSchema,
      prompt,
      temperature: 0.7, // Balanced creativity and consistency
    })
    const latency = Date.now() - startTime

    // Log interaction using existing structured logging
    console.log(`✅ [${correlationId}] AI suggestions generated`, {
      fieldName,
      suggestionsCount: object.suggestions.length,
      latency,
      userId: session.userId,
      provider: 'groq',
      model: 'llama-3.1-8b-instant'
    })

    // Track usage in Redis (use existing metrics system)
    await trackAIUsage(session.userId, 0.001, latency) // Estimated cost for Groq

    return NextResponse.json({
      success: true,
      correlationId,
      ...object
    })

  } catch (error) {
    console.error('❌ AI assist error:', error)

    // Return graceful fallback
    return NextResponse.json({
      success: false,
      error: 'AI assistance temporarily unavailable',
      suggestions: [{
        id: 'fallback',
        title: 'AI temporarily unavailable',
        description: 'Please try again in a moment or edit manually',
        newValue: '',
        confidence: 0
      }],
      reasoning: 'Service temporarily unavailable'
    }, { status: 200 }) // Return 200 for graceful degradation
  }
}

// Build context from consultation data
function buildAssistContext(body: AssistContext): AssistContext {
  return {
    fieldName: body.fieldName,
    currentValue: body.currentValue,
    fullConsultation: body.fullConsultation,
    patientAge: body.fullConsultation?.patient_details?.age,
    consultationType: body.fullConsultation?.consultation_type || 'outpatient',
    customRequest: body.customRequest
  }
}

// Simple data sanitization before sending to Groq
function sanitizeForGroq(consultation: any) {
  const sanitized = { ...consultation }
  
  // Remove obvious PII
  if (sanitized.patient_details?.name) {
    sanitized.patient_details.name = '[PATIENT]'
  }
  
  // Keep medical data for context (already anonymized in our system)
  return sanitized
}

// Build intelligent prompt for Groq
function buildPrompt(fieldName: string, currentValue: string, consultation: any, customRequest?: string): string {
  const basePrompt = `You are a medical AI assistant helping doctors improve consultation notes. 

FIELD TO IMPROVE: ${fieldName}
CURRENT VALUE: "${currentValue}"

FULL CONSULTATION CONTEXT:
${JSON.stringify(consultation, null, 2)}

${customRequest ? `SPECIFIC REQUEST: ${customRequest}` : ''}

INSTRUCTIONS:
1. Provide 2-3 helpful suggestions to improve the "${fieldName}" field
2. Consider the full medical context provided
3. Make suggestions more detailed, accurate, or professionally worded
4. Maintain medical accuracy and professional tone
5. Each suggestion should be meaningfully different
6. Include confidence scores based on medical appropriateness

Focus on making the content more:
- Medically accurate and specific
- Professionally worded
- Complete and comprehensive
- Contextually appropriate for the patient and consultation type`

  return basePrompt
}

// Simple cost tracking using existing Redis metrics
async function trackAIUsage(userId: string, estimatedCost: number, latency: number) {
  try {
    // This would integrate with existing Redis metrics system
    // For now, just log the usage
    console.log(`📊 AI usage tracked: userId=${userId}, cost=${estimatedCost}, latency=${latency}ms`)
  } catch (error) {
    console.error('Failed to track AI usage:', error)
  }
}
