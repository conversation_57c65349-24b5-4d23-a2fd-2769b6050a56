/**
 * CELER ASSIST API ENDPOINT
 * Frontend-only AI enhancement tool using Groq via Vercel AI SDK
 * 
 * Provides contextual suggestions for editing consultation JSON fields
 * Built on existing enterprise architecture without backend changes
 */

import { NextRequest, NextResponse } from 'next/server'
import { Groq } from 'groq-sdk'
import { verifySession } from '@/lib/auth/supabase-helpers'

// Initialize Groq client
const groq = new Groq({
  apiKey: process.env.GROQ_API_KEY
})

// Context interface for AI requests
interface AssistContext {
  fieldName: string
  currentValue: string
  fullConsultation: any
  patientAge?: number
  consultationType?: string
  customRequest?: string
}

export async function POST(request: NextRequest) {
  try {
    // Verify user session using existing auth system
    const session = await verifySession()
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Parse request body
    const body = await request.json()
    const { fieldName, currentValue, fullConsultation, customRequest } = body as AssistContext

    // Validate required fields
    if (!fieldName || currentValue === undefined || !fullConsultation) {
      return NextResponse.json(
        { error: 'Missing required fields: fieldName, currentValue, fullConsultation' },
        { status: 400 }
      )
    }

    // Build context for AI
    const context = buildAssistContext(body)

    // Generate correlation ID for tracking (use existing enterprise pattern)
    const correlationId = `assist_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

    console.log(`🤖 [${correlationId}] AI assist request for field: ${fieldName}`)

    // Sanitize data before sending to Groq (basic privacy protection)
    const sanitizedConsultation = sanitizeForGroq(fullConsultation)

    // Build prompt for Groq
    const prompt = buildPrompt(fieldName, currentValue, sanitizedConsultation, customRequest)

    // Call Groq using JSON mode (updated approach)
    const startTime = Date.now()
    const completion = await groq.chat.completions.create({
      model: 'llama-3.1-8b-instant',
      messages: [
        {
          role: 'system',
          content: `You are a medical AI assistant that returns suggestions in JSON format.

Your response must be a valid JSON object with this exact structure:
{
  "suggestions": [
    {
      "id": "string",
      "title": "string",
      "description": "string",
      "newValue": "string",
      "confidence": number (0.0 to 1.0)
    }
  ],
  "reasoning": "string"
}

IMPORTANT:
- Return ONLY the JSON object, no other text
- Include 2-3 suggestions maximum
- Each suggestion must have all required fields
- Confidence should be between 0.0 and 1.0`
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      response_format: { type: 'json_object' },
      temperature: 0.7,
      stream: false
    })

    const latency = Date.now() - startTime
    const responseContent = completion.choices[0].message.content

    // Parse and validate the JSON response
    const object = JSON.parse(responseContent)

    // Basic validation to ensure required fields exist
    if (!object.suggestions || !Array.isArray(object.suggestions) || !object.reasoning) {
      throw new Error('Invalid response structure from AI')
    }

    // Log interaction using existing structured logging
    console.log(`✅ [${correlationId}] AI suggestions generated`, {
      fieldName,
      suggestionsCount: object.suggestions.length,
      latency,
      userId: session.userId,
      provider: 'groq',
      model: 'llama-3.1-8b-instant'
    })

    // Track usage in Redis (use existing metrics system)
    await trackAIUsage(session.userId, 0.001, latency) // Estimated cost for Groq

    return NextResponse.json({
      success: true,
      correlationId,
      ...object
    })

  } catch (error) {
    console.error('❌ AI assist error:', error)

    // Return graceful fallback
    return NextResponse.json({
      success: false,
      error: 'AI assistance temporarily unavailable',
      suggestions: [{
        id: 'fallback',
        title: 'AI temporarily unavailable',
        description: 'Please try again in a moment or edit manually',
        newValue: '',
        confidence: 0
      }],
      reasoning: 'Service temporarily unavailable'
    }, { status: 200 }) // Return 200 for graceful degradation
  }
}

// Build context from consultation data
function buildAssistContext(body: AssistContext): AssistContext {
  return {
    fieldName: body.fieldName,
    currentValue: body.currentValue,
    fullConsultation: body.fullConsultation,
    patientAge: body.fullConsultation?.patient_details?.age,
    consultationType: body.fullConsultation?.consultation_type || 'outpatient',
    customRequest: body.customRequest
  }
}

// Simple data sanitization before sending to Groq
function sanitizeForGroq(consultation: any) {
  const sanitized = { ...consultation }
  
  // Remove obvious PII
  if (sanitized.patient_details?.name) {
    sanitized.patient_details.name = '[PATIENT]'
  }
  
  // Keep medical data for context (already anonymized in our system)
  return sanitized
}

// Build intelligent prompt for Groq
function buildPrompt(fieldName: string, currentValue: string, consultation: any, customRequest?: string): string {
  const basePrompt = `FIELD TO IMPROVE: ${fieldName}
CURRENT VALUE: "${currentValue}"

CONSULTATION CONTEXT:
${JSON.stringify(consultation, null, 2)}

${customRequest ? `SPECIFIC REQUEST: ${customRequest}` : ''}

TASK: Improve the "${fieldName}" field with 2-3 helpful suggestions.

REQUIREMENTS:
- Consider the full medical context
- Make suggestions more detailed, accurate, or professionally worded
- Maintain medical accuracy and professional tone
- Each suggestion should be meaningfully different
- Assign confidence scores (0.0 to 1.0) based on medical appropriateness

Focus on making the content more:
- Medically accurate and specific
- Professionally worded
- Complete and comprehensive
- Contextually appropriate for the patient and consultation type`

  return basePrompt
}

// Simple cost tracking using existing Redis metrics
async function trackAIUsage(userId: string, estimatedCost: number, latency: number) {
  try {
    // This would integrate with existing Redis metrics system
    // For now, just log the usage
    console.log(`📊 AI usage tracked: userId=${userId}, cost=${estimatedCost}, latency=${latency}ms`)
  } catch (error) {
    console.error('Failed to track AI usage:', error)
  }
}
