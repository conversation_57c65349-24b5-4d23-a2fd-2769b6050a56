'use client'

import { useState, useRef, useEffect, use<PERSON><PERSON>back, Profiler } from 'react'
import { Mic, Square, Play, Pause, Upload, Wand2, Edit3, Copy, X, ChevronDown, ChevronUp, Check, Loader2, Trash2, Save, Plus, Undo } from 'lucide-react'
import { Consultation, ConsultationType } from '@/lib/types'
// import { formatDuration } from '@/lib/utils'
import { motion, AnimatePresence } from 'framer-motion'
import { JsonDrivenEditor } from './json-driven-editor'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { createConsultationWithFiles, approveConsultation, addAdditionalAudio, addAdditionalImages, updateConsultationType, saveEditedNote, clearEditedNote, deleteAdditionalAudio, deleteConsultationImage, updatePatientName } from '@/lib/actions/consultations'
import { useAutosave } from '@/hooks/useAutosave'
import { AutoSaveIndicator } from '@/components/ui/autosave-indicator'
import { trackConsultation } from '@/lib/analytics'
import { useConsultationStore } from '@/lib/stores/consultation-store'
import type { ConsultationData } from '@/lib/stores/consultation-store'
import { copyFromRenderedUI } from '@/lib/utils'

import Image from 'next/image'

// Convert basic markdown to HTML for display
function convertMarkdownToHtml(markdown: string): string {
  return markdown
    // Convert **bold** to <strong>bold</strong>
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    // Convert *italic* to <em>italic</em>
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    // Convert line breaks to <br> tags
    .replace(/\n/g, '<br>')
    // Convert double line breaks to paragraphs
    .replace(/<br><br>/g, '</p><p>')
    // Split by paragraph breaks and wrap each section in <p> tags
    .split('</p><p>')
    .map(section => section.trim())
    .filter(section => section.length > 0)
    .map(section => `<p>${section}</p>`)
    .join('')
    // If no paragraphs were created, wrap the whole content in a single paragraph
    || `<p>${markdown.replace(/\n/g, '<br>')}</p>`
}

// Convert HTML back to markdown for editing
function convertHtmlToMarkdown(html: string): string {
  return html
    // Convert <strong> to **bold**
    .replace(/<strong>(.*?)<\/strong>/g, '**$1**')
    // Convert <em> to *italic*
    .replace(/<em>(.*?)<\/em>/g, '*$1*')
    // Convert <br> tags to line breaks
    .replace(/<br\s*\/?>/g, '\n')
    // Convert paragraphs to double line breaks
    .replace(/<\/p><p>/g, '\n\n')
    // Remove paragraph tags
    .replace(/<\/?p>/g, '')
    // Clean up any remaining HTML tags
    .replace(/<[^>]*>/g, '')
    // Decode HTML entities
    .replace(/</g, '<')
    .replace(/>/g, '>')
    .replace(/&/g, '&')
    .replace(/"/g, '"')
    .replace(/'/g, "'")
}

interface StreamlinedRecordingAreaProps {
  selectedConsultation: Consultation | null
  isDarkMode: boolean
  doctorId: string
  doctorName?: string
  onConsultationUpdate: (consultation: Consultation) => void
  // State props (patientName and selectedTemplate moved to Zustand store)
  isRecording: boolean
  isPaused: boolean
  recordingDuration: number
  audioBlob: Blob | null
  audioFile: File | null
  images: Array<{ id: string; file: File; preview?: string }>
  setImages: (images: Array<{ id: string; file: File; preview?: string }>) => void
  isGenerating: boolean
  setIsGenerating: (generating: boolean) => void
  summary: string
  setSummary: (summary: string) => void
  isEditing: boolean
  setIsEditing: (editing: boolean) => void
  additionalNotes: string
  setAdditionalNotes: (notes: string) => void
  // Function props
  startRecording: () => void
  pauseRecording: () => void
  stopRecording: () => void
  handleImageUpload: (files: FileList) => void
  removeImage: (id: string) => void
  clearAudio: () => void
}

const templates = [
  { id: 'outpatient', name: 'Outpatient Consultation', color: 'teal' },
  { id: 'discharge', name: 'Discharge Summary', color: 'purple' },
  { id: 'surgery', name: 'Operative Note', color: 'red' },
  { id: 'radiology', name: 'Radiology Report', color: 'blue' },
  { id: 'dermatology', name: 'Dermatology Note', color: 'green' },
  { id: 'cardiology_echo', name: 'Echocardiogram Report', color: 'pink' },
  { id: 'ivf_cycle', name: 'IVF Cycle Summary', color: 'orange' },
  { id: 'pathology', name: 'Histopathology Report', color: 'indigo' }
]

// Format duration in seconds to MM:SS format
const formatDuration = (seconds: number): string => {
  const mins = Math.floor(seconds / 60)
  const secs = seconds % 60
  return `${mins}:${secs.toString().padStart(2, '0')}`
}

// Performance monitoring callback for React Profiler
const onRenderCallback = (
  id: string,
  phase: 'mount' | 'update' | 'nested-update',
  actualDuration: number,
  baseDuration: number,
  startTime: number,
  commitTime: number
) => {
  // Only log in development or when performance monitoring is enabled
  if (process.env.NODE_ENV === 'development' || process.env.ENABLE_PERFORMANCE_MONITORING === 'true') {
    console.log('🔍 StreamlinedRecordingArea Performance:', {
      id,
      phase,
      actualDuration: Math.round(actualDuration * 100) / 100,
      baseDuration: Math.round(baseDuration * 100) / 100,
      startTime: Math.round(startTime),
      commitTime: Math.round(commitTime),
      timestamp: new Date().toISOString()
    })

    // Track slow renders (>16ms for 60fps)
    if (actualDuration > 16) {
      console.warn('⚠️ Slow render detected in StreamlinedRecordingArea:', {
        duration: actualDuration,
        phase,
        threshold: '16ms (60fps)'
      })
    }

    // Send to analytics in production if enabled
    if (typeof window !== 'undefined' && process.env.NODE_ENV === 'production') {
      // Import analytics dynamically to avoid SSR issues
      import('@/lib/analytics').then(({ trackPerformance }) => {
        if (trackPerformance) {
          trackPerformance({
            component: 'StreamlinedRecordingArea',
            phase,
            actualDuration,
            baseDuration,
            isSlowRender: actualDuration > 16
          })
        }
      }).catch(() => {
        // Silently fail analytics tracking
      })
    }
  }
}

export function StreamlinedRecordingArea(props: StreamlinedRecordingAreaProps) {
  const {
    selectedConsultation, isDarkMode, isRecording, isPaused, recordingDuration,
    audioBlob, images, isGenerating, setIsGenerating, summary, setSummary,
    isEditing, setIsEditing, additionalNotes, setAdditionalNotes,
    startRecording, pauseRecording, stopRecording, handleImageUpload, removeImage,
    clearAudio, doctorName
  } = props

  // Get form state from Zustand store (demonstrating reduced prop drilling)
  const patientName = useConsultationStore(state => state.patientName)
  const selectedTemplate = useConsultationStore(state => state.selectedTemplate)
  const setPatientName = useConsultationStore(state => state.setPatientName)
  const setSelectedTemplate = useConsultationStore(state => state.setSelectedTemplate)

  const [isTemplateOpen, setIsTemplateOpen] = useState(false)
  const [isNotesOpen, setIsNotesOpen] = useState(false)
  const [audioPlaying, setAudioPlaying] = useState<string | null>(null)
  const [isLoadingAudio, setIsLoadingAudio] = useState<string | null>(null)

  // Web Audio API refs for Safari
  const audioContextRef = useRef<AudioContext | null>(null)
  const audioSourceRef = useRef<AudioBufferSourceNode | null>(null)
  const [copySuccess, setCopySuccess] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isApproving, setIsApproving] = useState(false)

  const [submitMessage, setSubmitMessage] = useState('')
  // Ref for JsonDrivenEditor to access DOM for copying
  const jsonEditorRef = useRef<HTMLDivElement>(null)
  const [messageTimeoutRef, setMessageTimeoutRef] = useState<NodeJS.Timeout | null>(null)

  // Get Zustand store for consultation management
  const setCurrentConsultation = useConsultationStore(state => state.setCurrentConsultation)
  const undoLastChange = useConsultationStore(state => state.undoLastChange)
  const undoStack = useConsultationStore(state => state.undoStack)

  // Helper function to convert Consultation to ConsultationData
  const convertToConsultationData = (consultation: Consultation): ConsultationData => ({
    id: consultation.id,
    primary_audio_url: consultation.primary_audio_url || '',
    additional_audio_urls: Array.isArray(consultation.additional_audio_urls)
      ? consultation.additional_audio_urls.filter((url): url is string => typeof url === 'string')
      : consultation.additional_audio_urls
        ? [consultation.additional_audio_urls as string]
        : undefined,
    image_urls: Array.isArray(consultation.image_urls)
      ? consultation.image_urls.filter((url): url is string => typeof url === 'string')
      : consultation.image_urls
        ? [consultation.image_urls as string]
        : undefined,
    submitted_by: consultation.submitted_by || '',
    consultation_type: consultation.consultation_type || '',
    patient_name: consultation.patient_name || undefined,
    doctor_notes: consultation.doctor_notes || undefined,
    additional_notes: consultation.additional_notes || undefined,
    ai_generated_note_json: consultation.ai_generated_note_json,
    edited_note_json: consultation.edited_note_json,
    status: consultation.status as 'pending' | 'processing' | 'generated' | 'failed',
    created_at: consultation.created_at,
    updated_at: consultation.updated_at
  })

  // Autosave hook - OPTIMISTIC: Only show error states, no loading/success indicators
  const {
    autoSaveStatus,
    autoSaveImages,
    autoSaveAudio,
    autoSaveText,
    retryAutoSave,
    forceSave: _forceSave,
    hasPendingSaves: _hasPendingSaves
  } = useAutosave({
    onSuccess: (type) => {
      // console.log(`Autosave successful: ${type}`)
      // OPTIMISTIC: No success messages - users see final state immediately
    },
    onError: (type, error) => {
      // console.error(`Autosave failed: ${type}`, error)
      // Only show error states for retry functionality
    }
  })

  // Delete functionality state
  const [longPressTimer, setLongPressTimer] = useState<NodeJS.Timeout | null>(null)
  const [showDeleteMenu, setShowDeleteMenu] = useState<{ type: 'audio' | 'image', url: string } | null>(null)

  // Additional upload states for generated consultations (unused but kept for future features)
  const [_additionalImages, _setAdditionalImages] = useState<Array<{ id: string, url: string, preview?: string }>>([])
  const [_isRecordingAdditional, _setIsRecordingAdditional] = useState(false)
  const [_additionalAudioBlob, _setAdditionalAudioBlob] = useState<Blob | null>(null)
  const [_mediaRecorder, _setMediaRecorder] = useState<MediaRecorder | null>(null)

  const fileInputRef = useRef<HTMLInputElement>(null)
  const audioRefs = useRef<{ [key: string]: HTMLAudioElement }>({})

  // Autosave wrapper functions
  const handleAutoSaveImages = useCallback(async (imageFiles: File[]) => {
    if (!selectedConsultation) return

    const { addAdditionalImages } = await import('@/lib/actions/consultations')
    return addAdditionalImages(selectedConsultation.id, imageFiles)
  }, [selectedConsultation])

  const handleAutoSaveAudio = useCallback(async (audioBlob: Blob) => {
    if (!patientName.trim() || !selectedTemplate) {
      throw new Error('Patient name and template required')
    }

    if (selectedConsultation) {
      // Add additional audio to existing consultation
      // FIXED: Use unique timestamp to prevent overwriting in Cloudflare R2
      const audioFile = new File([audioBlob], `additional_audio_${Date.now()}.webm`, { type: 'audio/webm' })
      const { addAdditionalAudio } = await import('@/lib/actions/consultations')
      return addAdditionalAudio(selectedConsultation.id, audioFile)
    } else {
      // Create new consultation with seamless transition
      const audioFile = new File([audioBlob], 'audio.webm', { type: 'audio/webm' })
      const result = await createConsultationWithFiles(
        audioFile,
        images.map(img => img.file), // Include current images
        [],
        'doctor',
        additionalNotes || undefined,
        selectedTemplate as any,
        patientName || undefined
      )

      // SEAMLESS TRANSITION: If successful, trigger consultation update
      if (result && result.success && result.data) {
        // This will trigger the parent's onConsultationUpdate which clears local state
        props.onConsultationUpdate(result.data)
      }

      return result
    }
  }, [patientName, selectedTemplate, selectedConsultation, additionalNotes, images, props])

  const handleAutoSaveText = useCallback(async (field: string, value: string) => {
    if (!selectedConsultation) return

    if (field === 'notes') {
      const { updateAdditionalNotes } = await import('@/lib/actions/consultations')
      return updateAdditionalNotes(selectedConsultation.id, value)
    } else if (field === 'patient_name') {
      const { updatePatientName } = await import('@/lib/actions/consultations')
      return updatePatientName(selectedConsultation.id, value)
    }
  }, [selectedConsultation])

  // Helper function for seamless consultation refresh
  const refreshConsultationData = useCallback(async () => {
    if (!selectedConsultation) return

    try {
      const { getConsultations } = await import('@/lib/actions/consultations')
      const consultationsResult = await getConsultations()

      if (consultationsResult.success && consultationsResult.data) {
        const updatedConsultation = consultationsResult.data.consultations.find(c => c.id === selectedConsultation.id)
        if (updatedConsultation && props.onConsultationUpdate) {
          props.onConsultationUpdate(updatedConsultation)
        }
      }
    } catch (error) {
      // console.error('Failed to refresh consultation data:', error)
    }
  }, [selectedConsultation, props])

  // OPTIMISTIC IMAGE UPLOAD: Wait for final state, no local preview
  const handleImageUploadWithAutosave = useCallback(async (files: FileList) => {
    // Skip local state completely - wait for final state only
    if (selectedConsultation && files.length > 0) {
      const imageFiles = Array.from(files)

      try {
        // Upload and wait for final state - no local preview
        const result = await autoSaveImages(imageFiles, async (files: File[]) => {
          const uploadResult = await handleAutoSaveImages(files)

          // WAIT FOR FINAL STATE: Only refresh after successful upload
          if (uploadResult && typeof uploadResult === 'object' && 'success' in uploadResult && uploadResult.success) {
            // Refresh consultation data to show final URLs only
            await refreshConsultationData()
          }

          return uploadResult
        })

        // console.log('Image autosave completed:', result)
      } catch (error) {
        // console.error('Image autosave failed:', error)
        // On error, show local state as fallback
        handleImageUpload(files)
      }
    } else {
      // For new consultations without selectedConsultation, use local state as before
      handleImageUpload(files)
    }
  }, [selectedConsultation, autoSaveImages, handleAutoSaveImages, refreshConsultationData, handleImageUpload])

  // Clear messages when consultation changes
  useEffect(() => {
    setSubmitMessage('')
    setMessageTimeoutRef(prevTimeout => {
      if (prevTimeout) {
        clearTimeout(prevTimeout)
      }
      return null
    })
  }, [selectedConsultation?.id])

  // Sync selected consultation with Zustand store
  useEffect(() => {
    setCurrentConsultation(selectedConsultation ? convertToConsultationData(selectedConsultation) : null)
  }, [selectedConsultation, setCurrentConsultation])

  // EVENT-DRIVEN AUTOSAVE: Called directly from user actions, not reactive useEffect

  // Track previous audioBlob to detect when recording just finished
  const prevAudioBlobRef = useRef<Blob | null>(null)

  // OPTIMISTIC AUDIO UPLOAD: Skip local blob state, show final state immediately
  useEffect(() => {
    const hadNoAudio = prevAudioBlobRef.current === null
    const nowHasAudio = audioBlob !== null

    if (hadNoAudio && nowHasAudio && patientName.trim() && selectedTemplate) {
      // Recording just finished, trigger autosave with optimistic transition
      autoSaveAudio(audioBlob, async (blob: Blob) => {
        const result = await handleAutoSaveAudio(blob)

        // OPTIMISTIC: Clear local state immediately and show final state
        if (result && typeof result === 'object' && 'success' in result && result.success) {
          // Clear local audio state immediately (optimistic)
          clearAudio()

          // Refresh consultation data to show final audio URL
          await refreshConsultationData()
        }

        return result
      })
    }

    prevAudioBlobRef.current = audioBlob
  }, [audioBlob, patientName, selectedTemplate, autoSaveAudio, handleAutoSaveAudio, clearAudio, refreshConsultationData])

  // Wrapper for text input with autosave
  const handleNotesChangeWithAutosave = useCallback((value: string) => {
    setAdditionalNotes(value)

    // Trigger autosave for text (only if we have an existing consultation and actual content)
    if (selectedConsultation && value.trim()) {
      autoSaveText('notes', value, handleAutoSaveText)
    }
  }, [setAdditionalNotes, selectedConsultation, autoSaveText, handleAutoSaveText])

  // Wrapper for patient name input with autosave
  const handlePatientNameChangeWithAutosave = useCallback((value: string) => {
    setPatientName(value)

    // Trigger autosave for patient name (only if we have an existing consultation and actual content)
    if (selectedConsultation && value.trim()) {
      autoSaveText('patient_name', value, handleAutoSaveText)
    }
  }, [setPatientName, selectedConsultation, autoSaveText, handleAutoSaveText])

  // Cleanup long press timer on unmount
  useEffect(() => {
    return () => {
      if (longPressTimer) {
        clearTimeout(longPressTimer)
      }
    }
  }, [longPressTimer])

  // Function to set message with auto-clear timeout
  const setSubmitMessageWithTimeout = (message: string, timeout = 3000) => {
    // Clear any existing timeout
    if (messageTimeoutRef) {
      clearTimeout(messageTimeoutRef)
    }

    setSubmitMessage(message)

    // Set new timeout to clear message
    if (message) {
      const timeoutId = setTimeout(() => {
        setSubmitMessage('')
        setMessageTimeoutRef(null)
      }, timeout)
      setMessageTimeoutRef(timeoutId)
    }
  }

  const selectedTemplateData = templates.find(t => t.id === selectedTemplate) || templates[0]
  const isApproved = selectedConsultation?.status === 'approved'
  const canEdit = !isApproved
  // Check for JSON content (new system only)
  const hasJsonContent = selectedConsultation?.edited_note_json || selectedConsultation?.ai_generated_note_json
  const hasContent = hasJsonContent

  // UI-aware copy function handles all formatting

  const handleCopy = async () => {
    try {
      let textToCopy = ''

      // Use UI-aware copy function for JSON content
      if (hasJsonContent && jsonEditorRef.current) {
        textToCopy = copyFromRenderedUI(jsonEditorRef.current)
      }

      if (textToCopy) {
        await navigator.clipboard.writeText(textToCopy)
        setCopySuccess(true)
        setTimeout(() => setCopySuccess(false), 2000)
      }
    } catch (error) {
      // console.error('Failed to copy:', error)
    }
  }

  // Handle template change with immediate database sync
  const handleTemplateChange = async (newTemplate: string) => {
    setSelectedTemplate(newTemplate)

    // If we have a selected consultation, update it in the database immediately
    if (selectedConsultation) {
      try {
        const result = await updateConsultationType(
          selectedConsultation.id,
          newTemplate as ConsultationType
        )

        if (result.success) {
          // Update the consultation object to reflect the change
          const updatedConsultation = {
            ...selectedConsultation,
            consultation_type: newTemplate as ConsultationType
          }
          props.onConsultationUpdate(updatedConsultation)
        } else {
          // console.error('Failed to update consultation type:', result.error)
          setSubmitMessageWithTimeout(`Failed to update template: ${result.error}`)
        }
      } catch (error) {
        // console.error('Error updating consultation type:', error)
        setSubmitMessageWithTimeout('Failed to update template')
      }
    }
  }

  // Since JsonDrivenEditor handles inline editing with auto-save,
  // we don't need separate edit handlers anymore

  // Delete handlers
  const handleDeleteAudio = async (audioUrl: string) => {
    if (!selectedConsultation) return

    try {
      setSubmitMessage('Deleting audio...')
      const result = await deleteAdditionalAudio(selectedConsultation.id, audioUrl)

      if (result.success) {
        setSubmitMessageWithTimeout('Audio deleted successfully!')

        // Refresh consultation data
        const { getConsultations } = await import('@/lib/actions/consultations')
        const consultationsResult = await getConsultations()

        if (consultationsResult.success && consultationsResult.data) {
          const updatedConsultation = consultationsResult.data.consultations.find(c => c.id === selectedConsultation.id)
          if (updatedConsultation) {
            props.onConsultationUpdate(updatedConsultation)
          }
        }
      } else {
        setSubmitMessageWithTimeout(result.error || 'Failed to delete audio')
      }
    } catch (_error) {
      setSubmitMessageWithTimeout('Failed to delete audio')
    } finally {
      setShowDeleteMenu(null)
    }
  }

  const handleDeleteImage = async (imageUrl: string) => {
    if (!selectedConsultation) return

    try {
      setSubmitMessage('Deleting image...')
      const result = await deleteConsultationImage(selectedConsultation.id, imageUrl)

      if (result.success) {
        setSubmitMessageWithTimeout('Image deleted successfully!')

        // Refresh consultation data
        const { getConsultations } = await import('@/lib/actions/consultations')
        const consultationsResult = await getConsultations()

        if (consultationsResult.success && consultationsResult.data) {
          const updatedConsultation = consultationsResult.data.consultations.find(c => c.id === selectedConsultation.id)
          if (updatedConsultation) {
            props.onConsultationUpdate(updatedConsultation)
          }
        }
      } else {
        setSubmitMessageWithTimeout(result.error || 'Failed to delete image')
      }
    } catch (_error) {
      setSubmitMessageWithTimeout('Failed to delete image')
    } finally {
      setShowDeleteMenu(null)
    }
  }

  // Long press handlers for mobile
  const handleLongPressStart = (type: 'audio' | 'image', url: string) => {
    const timer = setTimeout(() => {
      setShowDeleteMenu({ type, url })
    }, 500) // 500ms long press
    setLongPressTimer(timer)
  }

  const handleLongPressEnd = () => {
    if (longPressTimer) {
      clearTimeout(longPressTimer)
      setLongPressTimer(null)
    }
  }

  const handleSubmitConsultation = async () => {
    // Check if we're working with an existing consultation or creating a new one
    const isExistingConsultation = !!selectedConsultation

    // Validation logic differs for existing vs new consultations
    if (isExistingConsultation) {
      // For existing consultations: we need either new audio, new images, or additional notes
      if (!audioBlob && images.length === 0 && !additionalNotes.trim()) {
        setSubmitMessage('Please add new audio, images, or additional notes to update the consultation')
        return
      }
    } else {
      // For new consultations: we need both audio and patient name
      if (!audioBlob || !patientName.trim()) {
        setSubmitMessage('Please provide patient name and record audio')
        return
      }
    }

    setIsSubmitting(true)
    setSubmitMessage('')

    try {
      if (isExistingConsultation) {
        // Handle new images
        if (images.length > 0) {
          const imageFiles = images.map(img => img.file)

          const imageResult = await addAdditionalImages(selectedConsultation.id, imageFiles)
          if (!imageResult.success) {
            setSubmitMessage(imageResult.error || 'Failed to add additional images')
            return
          }
        }

        // Handle new audio
        if (audioBlob) {
          const audioFile = new File([audioBlob], `additional_audio_${Date.now()}.webm`, {
            type: 'audio/webm'
          })

          const audioResult = await addAdditionalAudio(selectedConsultation.id, audioFile)
          if (!audioResult.success) {
            setSubmitMessage(audioResult.error || 'Failed to add additional audio')
            return
          }
        }

        // Handle additional notes update
        if (additionalNotes.trim() !== (selectedConsultation.additional_notes || '').trim()) {
          const { updateAdditionalNotes } = await import('@/lib/actions/consultations')
          const notesResult = await updateAdditionalNotes(selectedConsultation.id, additionalNotes.trim())
          if (!notesResult.success) {
            setSubmitMessage(notesResult.error || 'Failed to update additional notes')
            return
          }
        }

        setSubmitMessageWithTimeout('Consultation updated successfully!')

        // FIX: Refetch the consultation from database to get the latest state
        try {
          const { getConsultations } = await import('@/lib/actions/consultations')
          const consultationsResult = await getConsultations()

          if (consultationsResult.success && consultationsResult.data) {
            // Find the updated consultation in the fresh data
            const updatedConsultation = consultationsResult.data.consultations.find(c => c.id === selectedConsultation.id)

            if (updatedConsultation) {
              // Call onConsultationUpdate with the fresh data from database
              props.onConsultationUpdate(updatedConsultation)
              return { success: true, data: updatedConsultation }
            }
          }

          // Fallback if we can't find the updated consultation
          props.onConsultationUpdate(selectedConsultation)
          return { success: true, data: selectedConsultation }

        } catch (fetchError) {
          // console.error('Failed to refetch consultations:', fetchError)
          // Fallback to original consultation if fetch fails
          props.onConsultationUpdate(selectedConsultation)
          return { success: true, data: selectedConsultation }
        }

      } else {
        // Convert blob to File
        if (!audioBlob) {
          setSubmitMessage('No audio recording available')
          return
        }
        const audioFile = new File([audioBlob], `recording_${Date.now()}.webm`, {
          type: 'audio/webm'
        })

        // Extract File objects from images
        const imageFiles = images.map(img => img.file)

        const result = await createConsultationWithFiles(
          audioFile,
          imageFiles,
          [], // No additional audio files
          'doctor',
          additionalNotes || undefined,
          selectedTemplate as "outpatient" | "discharge" | "surgery" | "radiology" | "dermatology" | "cardiology_echo" | "ivf_cycle" | "pathology",
          patientName || undefined
        )

        if (result.success && result.data) {
          setSubmitMessageWithTimeout(`Consultation submitted! ${result.data.patient_name}`)
          props.onConsultationUpdate(result.data)
          return result
        } else {
          setSubmitMessage('error' in result ? result.error : 'Failed to submit consultation')
          return result
        }
      }
    } catch (error) {
      // console.error('❌ Save error:', error)
      setSubmitMessage(`Network error: ${error instanceof Error ? error.message : 'Unknown error'}. Please try again.`)
      return { success: false, error: 'Network error' }
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleGenerate = async (consultationToUse?: Consultation) => {
    // For existing consultations, use selectedConsultation directly
    // For new consultations, use the provided consultation from save result
    if (selectedConsultation) {
      // Using existing consultation for generation
    } else if (consultationToUse) {
      // Using newly created consultation for generation
    } else if (audioBlob) {
      // No consultation but have audio - save first then generate
      const result = await handleSubmitConsultation()
      if (result && result.success && result.data) {
        return handleGenerate(result.data)
      }
      return
    } else {
      setSubmitMessage('Please record audio or select a consultation first')
      return
    }

    const consultationId = selectedConsultation?.id || consultationToUse?.id
    const consultationType = selectedTemplate // Use current UI selection
    const doctorNotes = selectedConsultation?.doctor_notes || consultationToUse?.doctor_notes

    if (!consultationId) {
      setSubmitMessage('No consultation ID available')
      return
    }

    setIsGenerating(true)
    setSummary('')

    // Clear edited note when regenerating
    if (selectedConsultation) {
      try {
        await clearEditedNote(selectedConsultation.id)
      } catch (error) {
        // console.error('Failed to clear edited note:', error)
        // Continue with generation even if clearing fails
      }
    }

    try {
      const currentConsultation = selectedConsultation || consultationToUse
      if (!currentConsultation) {
        setSubmitMessage('No consultation data available')
        setIsGenerating(false)
        return
      }

      // Dispatch Pub/Sub job instead of streaming
      const response = await fetch('/api/generate-summary-stream', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          consultation_id: currentConsultation.id, // CRITICAL: Pass consultation ID for regeneration
          primary_audio_url: currentConsultation.primary_audio_url,
          additional_audio_urls: Array.isArray(currentConsultation.additional_audio_urls) ? currentConsultation.additional_audio_urls : [],
          image_urls: Array.isArray(currentConsultation.image_urls) ? currentConsultation.image_urls : [],
          submitted_by: currentConsultation.submitted_by || 'doctor',
          consultation_type: consultationType || 'outpatient',
          doctor_notes: doctorNotes || undefined,
          additional_notes: additionalNotes || undefined,
          patient_name: currentConsultation.patient_name || undefined,
          doctor_name: doctorName || undefined,
          created_at: currentConsultation.created_at || undefined,
        }),
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const result = await response.json()

      if (!result.success) {
        throw new Error(result.error || 'Failed to dispatch job')
      }

      console.log('✅ Job dispatched successfully:', result.consultationId)
      setSubmitMessageWithTimeout('AI generation started. Please wait...')

      // Start polling for completion
      startPollingForCompletion(result.consultationId, currentConsultation, consultationType)

    } catch (error) {
      console.error('❌ Generate error:', error)
      setSubmitMessage(`Failed to start generation: ${error instanceof Error ? error.message : 'Unknown error'}`)
      setIsGenerating(false)
    }
  }

  // New polling function for job completion
  const startPollingForCompletion = (consultationId: string, consultation: Consultation, consultationType: string) => {
    const pollInterval = setInterval(async () => {
      try {
        const response = await fetch(`/api/job-status/${consultationId}`)

        if (response.status === 503) {
          // Service unavailable - Redis is down, keep polling
          console.log('⚠️ Status service temporarily unavailable, continuing to poll...')
          return
        }

        if (response.status === 202) {
          // Still processing
          console.log('⏳ Still processing...')
          return
        }

        if (response.ok) {
          const data = await response.json()

          if (data.status === 'generated') {
            // Success! Update UI and stop polling
            clearInterval(pollInterval)
            setIsGenerating(false)

            // Update consultation with JSON data
            if (props.onConsultationUpdate) {
              props.onConsultationUpdate({
                ...consultation,
                ai_generated_note_json: data.data,
                status: 'generated',
                updated_at: data.updatedAt
              })
            }

            setSubmitMessageWithTimeout('Summary generated successfully!')
            trackConsultation('generated', consultationType)

          } else if (data.status === 'failed') {
            // Failed
            clearInterval(pollInterval)
            setIsGenerating(false)
            setSubmitMessage(data.error || 'Generation failed')
          }
        }
      } catch (error) {
        console.error('Polling error:', error)
        // Don't stop polling on network errors - keep trying
      }
    }, 3000) // Poll every 3 seconds

    // Stop polling after 10 minutes (timeout)
    setTimeout(() => {
      clearInterval(pollInterval)
      if (isGenerating) {
        setIsGenerating(false)
        setSubmitMessage('Generation timed out. Please try again.')
      }
    }, 600000) // 10 minutes
  }

  const handleApprove = async () => {
    if (!selectedConsultation || !summary.trim()) {
      setSubmitMessage('Please generate a summary before approving')
      return
    }

    setIsApproving(true)
    setSubmitMessage('')

    try {
      const result = await approveConsultation(selectedConsultation.id, summary)

      if (result.success) {
        setSubmitMessageWithTimeout('Consultation approved successfully!')
        trackConsultation('approved', selectedConsultation.consultation_type)

        if (props.onConsultationUpdate) {
          props.onConsultationUpdate({
            ...selectedConsultation,
            status: 'approved',
            edited_note: summary
          })
        }
      } else {
        setSubmitMessage(result.error || 'Failed to approve consultation')
      }
    } catch (_error) {
      setSubmitMessage('Failed to approve consultation')
    } finally {
      setIsApproving(false)
    }
  }

  // Web Audio API playback for Safari
  const handleWebAudioPlayback = async (audioId: string, audioUrl: string) => {
    if (audioPlaying === audioId) {
      if (audioSourceRef.current) {
        audioSourceRef.current.stop()
        audioSourceRef.current = null
      }
      setAudioPlaying(null)
      return
    }

    if (audioSourceRef.current) {
      audioSourceRef.current.stop()
    }

    setIsLoadingAudio(audioId)
    setAudioPlaying(null)

    try {
      if (!audioContextRef.current) {
        audioContextRef.current = new (window.AudioContext || (window as any).webkitAudioContext)()
      }
      if (audioContextRef.current.state === 'suspended') {
        await audioContextRef.current.resume()
      }

      const audioContext = audioContextRef.current
      const proxyUrl = `/api/audio-proxy?url=${encodeURIComponent(audioUrl)}`
      const response = await fetch(proxyUrl)
      if (!response.ok) {
        throw new Error(`Audio fetch failed: ${response.statusText}`)
      }
      const audioData = await response.arrayBuffer()
      const audioBuffer = await audioContext.decodeAudioData(audioData)
      const source = audioContext.createBufferSource()
      source.buffer = audioBuffer
      source.connect(audioContext.destination)
      source.start(0)

      source.onended = () => {
        setAudioPlaying(null)
        audioSourceRef.current = null
      }

      audioSourceRef.current = source
      setAudioPlaying(audioId)

    } catch (error) {
      // console.error('Web Audio API Error:', error)
      const errorMessage = error instanceof Error ? error.message : String(error)
      setSubmitMessage(`Unable to play audio: ${errorMessage}`)
      setAudioPlaying(null)
    } finally {
      setIsLoadingAudio(null)
    }
  }

  const toggleAudioPlayback = async (audioId: string) => {
    const audio = audioRefs.current[audioId]
    if (!audio) return

    const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent)

    if (isSafari && audio.src && !audio.src.startsWith('blob:')) {
      await handleWebAudioPlayback(audioId, audio.src)
      return
    }

    if (audioPlaying === audioId) {
      audio.pause()
      setAudioPlaying(null)
    } else {
      Object.values(audioRefs.current).forEach(a => a.pause())
      try {
        await audio.play()
        setAudioPlaying(audioId)
      } catch (error) {
        // console.error('Audio playback error:', error)
        setAudioPlaying(null)
        const errorMessage = error instanceof Error ? error.message : String(error)
        setSubmitMessage(`Unable to play audio: ${errorMessage}`)
      }
    }
  }

  return (
    <Profiler id="StreamlinedRecordingArea" onRender={onRenderCallback}>
      <div className="space-y-8 max-w-5xl">
      {/* Mobile: Patient Name, Template, Approve Button */}
      <div className="flex flex-col sm:hidden gap-4 pl-3">
        {/* Patient Name with Add Button */}
        <div>
          <div className="flex items-center justify-between mb-4">
            <label className={`text-lg font-semibold ${
              isDarkMode ? 'text-gray-200' : 'text-slate-800'
            }`}>
              Patient Name
            </label>
            {/* Add Consultation Button - only show when viewing existing consultation, positioned on the right */}
            {selectedConsultation && (
              <button
                onClick={() => props.onConsultationUpdate(null as any)}
                className={`w-8 h-8 rounded-full flex items-center justify-center transition-all duration-300 transform hover:scale-110 mr-8 ${
                  isDarkMode
                    ? 'bg-indigo-600 hover:bg-indigo-700 text-white shadow-lg'
                    : 'bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white shadow-lg hover:shadow-xl'
                }`}
                title="Add New Consultation"
              >
                <Plus className="w-4 h-4" />
              </button>
            )}
          </div>
          <Input
            value={patientName}
            onChange={(e) => selectedConsultation ? handlePatientNameChangeWithAutosave(e.target.value) : setPatientName(e.target.value)}
            placeholder="Enter patient name"
            className={`h-10 px-4 py-2 rounded-xl border transition-colors ${
              isDarkMode
                ? 'bg-black border-gray-700 text-gray-100 placeholder-gray-400 focus:border-indigo-500'
                : 'bg-white/70 backdrop-blur-sm border-white/30 text-slate-800 placeholder-slate-400 focus:border-indigo-500'
            } focus:outline-none focus:ring-2 focus:ring-teal-500/20`}
          />
        </div>

        {/* Template and Approve Button on same row */}
        <div className="flex gap-4 items-end">
          <div className="flex-1">
            <label className={`block text-lg font-semibold mb-4 ${
              isDarkMode ? 'text-gray-200' : 'text-slate-800'
            }`}>
              Template
            </label>
            <div className="relative">
              <Button
                variant="outline"
                onClick={() => canEdit && setIsTemplateOpen(!isTemplateOpen)}
                disabled={!canEdit}
                className={`w-full justify-between h-10 px-4 py-2 rounded-xl border transition-colors ${
                  isDarkMode
                    ? 'bg-black border-gray-700 text-gray-100 hover:bg-gray-800 focus:border-indigo-500'
                    : 'bg-white/70 backdrop-blur-sm border-white/30 text-slate-800 hover:bg-white/90 focus:border-indigo-500'
                } focus:outline-none focus:ring-2 focus:ring-indigo-500/20`}
              >
                <span>{selectedTemplateData.name}</span>
                {canEdit && <ChevronDown className="h-4 w-4" />}
              </Button>

              <AnimatePresence>
                {isTemplateOpen && canEdit && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className={`absolute top-full left-0 right-0 mt-1 border rounded-md shadow-lg z-20 ${
                      isDarkMode
                        ? 'bg-gray-950 border-gray-700'
                        : 'bg-white/90 backdrop-blur-xl border-white/30'
                    }`}
                  >
                    {templates.map((template) => (
                      <button
                        key={template.id}
                        onClick={() => {
                          handleTemplateChange(template.id)
                          setIsTemplateOpen(false)
                        }}
                        className={`w-full px-3 py-2 text-left transition-colors ${
                          selectedTemplate === template.id
                            ? isDarkMode
                              ? 'bg-indigo-900/30 text-indigo-400'
                              : 'bg-indigo-50 text-indigo-700'
                            : isDarkMode
                              ? 'hover:bg-gray-800 text-gray-200'
                              : 'hover:bg-indigo-50 text-slate-800'
                        }`}
                      >
                        {template.name}
                      </button>
                    ))}
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </div>

          {/* Mobile Approve Button - Same level as template */}
          {hasContent && selectedConsultation && !isApproved && (
            <Button
              onClick={handleApprove}
              disabled={isApproving || !hasContent}
              className="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 rounded-full px-6 py-2 font-medium w-32 justify-center"
            >
              {isApproving ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Approving...
                </>
              ) : (
                <>
                  <Check className="w-4 h-4 mr-2" />
                  Approve
                </>
              )}
            </Button>
          )}
        </div>
      </div>

      {/* Desktop: Patient Name, Template, Approve Button */}
      <div className="hidden sm:block pl-3">
        <div className="flex gap-6 items-end">
        {/* Patient Name - 25% size */}
        <div className="w-[25%]">
          <label className={`block text-lg font-semibold mb-4 ${
            isDarkMode ? 'text-gray-200' : 'text-slate-800'
          }`}>
            Patient Name
          </label>
          <Input
            value={patientName}
            onChange={(e) => selectedConsultation ? handlePatientNameChangeWithAutosave(e.target.value) : setPatientName(e.target.value)}
            placeholder="Enter patient name"
            className={`h-10 px-4 py-2 rounded-xl border transition-colors ${
              isDarkMode
                ? 'bg-black border-gray-700 text-gray-100 placeholder-gray-400 focus:border-indigo-500'
                : 'bg-white/70 backdrop-blur-sm border-white/30 text-slate-800 placeholder-slate-400 focus:border-indigo-500'
            } focus:outline-none focus:ring-2 focus:ring-indigo-500/20`}
          />
        </div>

        {/* Template - 25% size */}
        <div className="w-[25%]">
          <label className={`block text-lg font-semibold mb-4 ${
            isDarkMode ? 'text-gray-200' : 'text-slate-800'
          }`}>
            Template
          </label>
          <div className="relative">
            <Button
              variant="outline"
              onClick={() => canEdit && setIsTemplateOpen(!isTemplateOpen)}
              disabled={!canEdit}
              className={`w-full justify-between h-10 px-4 py-2 rounded-xl border transition-colors ${
                isDarkMode
                  ? 'bg-black border-gray-700 text-gray-100 hover:bg-gray-800 focus:border-indigo-500'
                  : 'bg-white/70 backdrop-blur-sm border-white/30 text-slate-800 hover:bg-white/90 focus:border-indigo-500'
              } focus:outline-none focus:ring-2 focus:ring-indigo-500/20`}
            >
              <span>{selectedTemplateData.name}</span>
              {canEdit && <ChevronDown className="h-4 w-4" />}
            </Button>

            <AnimatePresence>
              {isTemplateOpen && canEdit && (
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  className={`absolute top-full left-0 right-0 mt-1 border rounded-md shadow-lg z-20 ${
                    isDarkMode
                      ? 'bg-gray-950 border-gray-700'
                      : 'bg-white/90 backdrop-blur-xl border-white/30'
                  }`}
                >
                  {templates.map((template) => (
                    <button
                      key={template.id}
                      onClick={() => {
                        handleTemplateChange(template.id)
                        setIsTemplateOpen(false)
                      }}
                      className={`w-full px-3 py-2 text-left transition-colors ${
                        selectedTemplate === template.id
                          ? isDarkMode
                            ? 'bg-indigo-900/30 text-indigo-400'
                            : 'bg-indigo-50 text-indigo-700'
                          : isDarkMode
                            ? 'hover:bg-gray-800 text-gray-200'
                            : 'hover:bg-indigo-50 text-slate-800'
                      }`}
                    >
                      {template.name}
                    </button>
                  ))}
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>

        {/* Action Buttons - 50% size */}
        <div className="flex items-end space-x-3 w-[50%]">
          {hasContent && selectedConsultation && !isApproved && (
            <Button
              onClick={handleApprove}
              disabled={isApproving || !hasContent}
              className="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 rounded-full px-6 py-2 font-medium w-32 justify-center"
            >
              {isApproving ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Approving...
                </>
              ) : (
                <>
                  <Check className="w-4 h-4 mr-2" />
                  Approve
                </>
              )}
            </Button>
          )}

          {/* Desktop Generate/Regenerate Button */}
          <AnimatePresence>
            {(selectedConsultation || (audioBlob && autoSaveStatus === 'saved')) && !isApproved && (
              <motion.div
                initial={{ opacity: 0, scale: 0.8, x: -20 }}
                animate={{ opacity: 1, scale: 1, x: 0 }}
                exit={{ opacity: 0, scale: 0.8, x: -20 }}
                transition={{ type: "spring", damping: 20, stiffness: 300 }}
              >
                <Button
                  onClick={() => handleGenerate()}
                  disabled={isGenerating}
                  className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 rounded-full px-6 py-2 font-medium w-32 justify-center"
                >
                  {isGenerating ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Creating...
                    </>
                  ) : (
                    <>
                      <Wand2 className="w-4 h-4 mr-2" />
                      {(selectedConsultation && (selectedConsultation.ai_generated_note || selectedConsultation.edited_note)) ? 'Regenerate' : 'Generate'}
                    </>
                  )}
                </Button>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
        </div>
      </div>

      {/* Recording Controls */}
      <div className="pl-3">
        {/* Mobile: Recording controls */}
        <div className="flex sm:hidden items-center justify-between">
          <div className="flex items-center gap-3">
            {!isRecording ? (
              <Button
                onClick={startRecording}
                disabled={isApproved}
                size="sm"
                className="bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white shadow-md rounded-full w-10 h-10 p-0"
              >
                <Mic className="w-4 h-4" />
              </Button>
            ) : (
              <div className="flex items-center gap-2">
                <Button
                  onClick={pauseRecording}
                  size="sm"
                  variant="outline"
                  className="border-yellow-500 text-yellow-600 hover:bg-yellow-50 rounded-full w-10 h-10 p-0"
                >
                  {isPaused ? <Play className="w-4 h-4" /> : <Pause className="w-4 h-4" />}
                </Button>
                <Button
                  onClick={stopRecording}
                  size="sm"
                  variant="outline"
                  className="border-red-500 text-red-600 hover:bg-red-50 rounded-full w-10 h-10 p-0"
                >
                  <Square className="w-4 h-4" />
                </Button>
                <div className="flex items-center gap-1 ml-2">
                  <Badge variant="secondary" className="font-mono text-sm px-2 py-1">
                    {formatDuration(recordingDuration)}
                  </Badge>
                  {isPaused && (
                    <Badge variant="outline" className="text-yellow-600 border-yellow-500 text-xs">
                      Paused
                    </Badge>
                  )}
                </div>
              </div>
            )}

            <Button
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                setTimeout(() => {
                  if (fileInputRef.current) {
                    fileInputRef.current.click();
                  }
                }, 0);
              }}
              disabled={isApproved}
              variant="outline"
              size="sm"
              className={`w-10 h-10 p-0 rounded-full transition-all duration-300 transform hover:scale-110 ${
                isDarkMode
                  ? 'bg-gray-800 border-gray-600 text-gray-300 hover:bg-gray-700 hover:border-gray-500'
                  : 'bg-gradient-to-r from-indigo-50 to-purple-50 border-indigo-200 text-indigo-600 hover:from-indigo-100 hover:to-purple-100 hover:border-indigo-300'
              }`}
              title="Upload Images"
            >
              <Upload className="w-4 h-4" />
            </Button>
          </div>
        </div>

        {/* Desktop: Multi-column layout */}
        <div className="hidden sm:block">
          <div className="flex gap-4 items-center">
            <div className="flex items-center gap-3 flex-1 max-w-xs">
              {!isRecording ? (
                <Button
                  onClick={startRecording}
                  disabled={isApproved}
                  size="sm"
                  className="bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white shadow-md rounded-full w-10 h-10 p-0"
                >
                  <Mic className="w-4 h-4" />
                </Button>
              ) : (
                <div className="flex items-center gap-2">
                  <Button
                    onClick={pauseRecording}
                    size="sm"
                    variant="outline"
                    className="border-yellow-500 text-yellow-600 hover:bg-yellow-50 rounded-full w-10 h-10 p-0"
                  >
                    {isPaused ? <Play className="w-4 h-4" /> : <Pause className="w-4 h-4" />}
                  </Button>
                  <Button
                    onClick={stopRecording}
                    size="sm"
                    variant="outline"
                    className="border-red-500 text-red-600 hover:bg-red-50 rounded-full w-10 h-10 p-0"
                  >
                    <Square className="w-4 h-4" />
                  </Button>
                  <div className="flex items-center gap-2 ml-3">
                    <Badge variant="secondary" className="font-mono text-lg px-3 py-1">
                      {formatDuration(recordingDuration)}
                    </Badge>
                    {isPaused && (
                      <Badge variant="outline" className="text-yellow-600 border-yellow-500">
                        Paused
                      </Badge>
                    )}
                  </div>
                </div>
              )}

              <Button
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  setTimeout(() => {
                    if (fileInputRef.current) {
                      fileInputRef.current.click();
                    }
                  }, 0);
                }}
                disabled={isApproved}
                variant="outline"
                size="sm"
                className={`w-10 h-10 p-0 rounded-full transition-all duration-300 transform hover:scale-110 ${
                  isDarkMode
                    ? 'bg-gray-800 border-gray-600 text-gray-300 hover:bg-gray-700 hover:border-gray-500'
                    : 'bg-gradient-to-r from-indigo-50 to-purple-50 border-indigo-200 text-indigo-600 hover:from-indigo-100 hover:to-purple-100 hover:border-indigo-300'
                }`}
                title="Upload Images"
              >
                <Upload className="w-4 h-4" />
              </Button>

              <AutoSaveIndicator
                status={autoSaveStatus}
                onRetry={() => retryAutoSave(async () => {
                  if (audioBlob && patientName.trim() && selectedTemplate) {
                    return handleAutoSaveAudio(audioBlob)
                  }
                  throw new Error('Nothing to save')
                })}
                showText={false}
                className="ml-2"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Status Message */}
        {submitMessage && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className={`mt-4 p-3 rounded-md ${
              isDarkMode
                ? 'bg-blue-950/50 border border-blue-800'
                : 'bg-blue-50 border border-blue-200'
            }`}
          >
            <p className={`text-sm ${
              isDarkMode ? 'text-blue-200' : 'text-blue-800'
            }`}>{submitMessage}</p>
          </motion.div>
        )}

      {/* Audio Recordings */}
      {(audioBlob || selectedConsultation?.primary_audio_url || (selectedConsultation?.additional_audio_urls && Array.isArray(selectedConsultation.additional_audio_urls) && selectedConsultation.additional_audio_urls.filter((url): url is string => typeof url === 'string' && url.trim() !== '').length > 0)) && (
        <div className="flex flex-wrap gap-3 pl-0">
          {audioBlob && (
            <div className={`flex items-center gap-2 rounded-lg px-3 py-2 ${
              isDarkMode ? 'bg-black' : 'bg-transparent'
            }`}>
              <Button
                size="sm"
                variant="ghost"
                onClick={() => toggleAudioPlayback('new')}
                className="!h-10 !w-10 p-0 rounded-full bg-blue-600 hover:bg-blue-700 text-white"
                style={{ height: '40px', width: '40px' }}
              >
                {audioPlaying === 'new' ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
              </Button>
              <Button
                size="sm"
                variant="ghost"
                onClick={() => {
                  setAudioPlaying(null)
                  clearAudio()
                }}
                className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
              >
                <Trash2 className="w-3 h-3" />
              </Button>
              <audio
                ref={(el) => { if (el) audioRefs.current['new'] = el }}
                src={URL.createObjectURL(audioBlob)}
                onEnded={() => setAudioPlaying(null)}
              />
            </div>
          )}

          {selectedConsultation?.primary_audio_url && (
            <div className={`flex items-center gap-2 rounded-lg px-3 py-2 ${
              isDarkMode ? 'bg-black' : 'bg-transparent'
            }`}>
              <Button
                size="sm"
                variant="ghost"
                onClick={() => toggleAudioPlayback('primary')}
                disabled={isLoadingAudio === 'primary'}
                className="!h-10 !w-10 p-0 rounded-full bg-orange-600 hover:bg-orange-700 text-white disabled:opacity-50"
                style={{ height: '40px', width: '40px' }}
              >
                {isLoadingAudio === 'primary' ? (
                  <Loader2 className="w-4 h-4 animate-spin" />
                ) : audioPlaying === 'primary' ? (
                  <Pause className="w-4 h-4" />
                ) : (
                  <Play className="w-4 h-4" />
                )}
              </Button>
              <audio
                key={`primary-audio-${selectedConsultation.primary_audio_url}`}
                ref={(el) => {
                  if (el) {
                    audioRefs.current['primary'] = el
                    el.load()
                  }
                }}
                src={selectedConsultation.primary_audio_url}
                onEnded={() => setAudioPlaying(null)}
                onLoadedData={() => {
                  // Primary audio loaded
                }}
              />
            </div>
          )}

          {selectedConsultation?.additional_audio_urls &&
           Array.isArray(selectedConsultation.additional_audio_urls) &&
           selectedConsultation.additional_audio_urls
             .filter((url): url is string => typeof url === 'string' && url.trim() !== '')
             .map((url, index) => (
               <div
                 key={`additional-${index}`}
                 className={`flex items-center gap-2 rounded-lg px-3 py-2 relative group ${
                   isDarkMode ? 'bg-black' : 'bg-transparent'
                 }`}
                 onMouseEnter={() => selectedConsultation?.status !== 'approved' && setShowDeleteMenu({ type: 'audio', url })}
                 onMouseLeave={() => setShowDeleteMenu(null)}
                 onTouchStart={() => selectedConsultation?.status !== 'approved' && handleLongPressStart('audio', url)}
                 onTouchEnd={handleLongPressEnd}
                 onTouchCancel={handleLongPressEnd}
               >
                 <Button
                   size="sm"
                   variant="ghost"
                   onClick={() => toggleAudioPlayback(`additional-${index}`)}
                   disabled={isLoadingAudio === `additional-${index}`}
                   className="!h-10 !w-10 p-0 rounded-full bg-teal-600 hover:bg-teal-700 text-white disabled:opacity-50"
                   style={{ height: '40px', width: '40px' }}
                 >
                   {isLoadingAudio === `additional-${index}` ? (
                     <Loader2 className="w-4 h-4 animate-spin" />
                   ) : audioPlaying === `additional-${index}` ? (
                     <Pause className="w-4 h-4" />
                   ) : (
                     <Play className="w-4 h-4" />
                   )}
                 </Button>

                 {selectedConsultation?.status !== 'approved' && showDeleteMenu?.type === 'audio' && showDeleteMenu?.url === url && (
                   <Button
                     size="sm"
                     onClick={() => handleDeleteAudio(url)}
                     className="absolute -top-2 -right-2 h-6 w-6 p-0 rounded-full bg-red-500 hover:bg-red-600 text-white border-2 border-white shadow-lg z-10 transition-all duration-200"
                     title="Delete audio"
                   >
                     <Trash2 className="w-3 h-3" />
                   </Button>
                 )}

                 <audio
                   key={`additional-audio-${url}`}
                   ref={(el) => {
                     if (el) {
                       audioRefs.current[`additional-${index}`] = el
                       el.load()
                     }
                   }}
                   src={url}
                   onEnded={() => setAudioPlaying(null)}
                   onLoadedData={() => {
                      // Additional audio loaded
                   }}
                 />
               </div>
             ))}
        </div>
      )}

      {/* Images Preview */}
      {(selectedConsultation?.image_urls && Array.isArray(selectedConsultation.image_urls) && selectedConsultation.image_urls.length > 0) && (
        <div className={`p-4 rounded-lg transition-colors ${
          isDarkMode
            ? 'bg-black'
            : 'bg-transparent'
        }`}>
          <div className="flex flex-wrap gap-3">
            {Array.isArray(selectedConsultation?.image_urls) &&
             selectedConsultation.image_urls
               .filter((url): url is string => typeof url === 'string' && url.trim() !== '')
               .map((url, index) => (
              <div
                key={`existing-${index}`}
                className={`w-20 h-20 rounded-lg overflow-hidden border-2 relative group ${
                  isDarkMode
                    ? 'bg-black border-gray-700'
                    : 'bg-white/70 backdrop-blur-sm border-white/30'
                }`}
                onMouseEnter={() => selectedConsultation?.status !== 'approved' && setShowDeleteMenu({ type: 'image', url })}
                onMouseLeave={() => setShowDeleteMenu(null)}
                onTouchStart={() => selectedConsultation?.status !== 'approved' && handleLongPressStart('image', url)}
                onTouchEnd={handleLongPressEnd}
                onTouchCancel={handleLongPressEnd}
              >
                <Image
                  src={url}
                  alt={`Image ${index + 1}`}
                  width={80}
                  height={80}
                  className="w-full h-full object-cover"
                />

                {selectedConsultation?.status !== 'approved' && showDeleteMenu?.type === 'image' && showDeleteMenu?.url === url && (
                  <Button
                    size="sm"
                    onClick={() => handleDeleteImage(url)}
                    className="absolute -top-2 -right-2 h-6 w-6 p-0 rounded-full bg-red-500 hover:bg-red-600 text-white border-2 border-white shadow-lg z-10 transition-all duration-200"
                    title="Delete image"
                  >
                    <Trash2 className="w-3 h-3" />
                  </Button>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Additional Notes Dropdown */}
      <div className="w-full">
        <div className="flex items-center justify-between">
          <Button
            variant="ghost"
            onClick={() => setIsNotesOpen(!isNotesOpen)}
            className={`flex items-center gap-2 transition-colors ${
              isDarkMode
                ? 'text-gray-300 hover:text-gray-200'
                : 'text-slate-600 hover:text-slate-700'
            }`}
          >
            <span>Additional Notes</span>
            {isNotesOpen ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
          </Button>

          <div className="sm:hidden">
            {(selectedConsultation || (audioBlob && autoSaveStatus === 'saved')) && !isApproved && (
              <Button
                onClick={() => handleGenerate()}
                disabled={isGenerating}
                className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 rounded-full px-6 py-2 font-medium w-32 justify-center"
              >
                {isGenerating ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Creating...
                  </>
                ) : (
                  <>
                    <Wand2 className="w-4 h-4 mr-2" />
                    {(selectedConsultation && (selectedConsultation.ai_generated_note || selectedConsultation.edited_note)) ? 'Regenerate' : 'Generate'}
                  </>
                )}
              </Button>
            )}
          </div>
        </div>

        <AnimatePresence>
          {isNotesOpen && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="mt-3"
            >
              <div className={`p-4 pr-4 sm:pr-20 rounded-lg transition-colors ${
                isDarkMode
                  ? 'bg-black'
                  : 'bg-transparent'
              }`}>
                <textarea
                  value={additionalNotes}
                  onChange={(e) => handleNotesChangeWithAutosave(e.target.value)}
                  placeholder="Add any additional notes or observations..."
                  rows={6}
                  className={`w-full px-3 py-2 border rounded-xl resize-none focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-colors ${
                    isDarkMode
                      ? 'bg-black border-gray-700 text-gray-100 placeholder-gray-400'
                      : 'bg-white/70 backdrop-blur-sm border-white/30 text-slate-800 placeholder-slate-400'
                  }`}
                />

                <div className="mt-2 flex justify-end">
                  <AutoSaveIndicator
                    status={autoSaveStatus}
                    onRetry={() => retryAutoSave(async () => {
                      if (audioBlob && patientName.trim() && selectedTemplate) {
                        return handleAutoSaveAudio(audioBlob)
                      }
                      throw new Error('Nothing to save')
                    })}
                    showText={false}
                  />
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Summary Section */}
      {(isGenerating || hasContent) && (
        <div className={`py-6 pr-4 sm:pr-20 pl-2 rounded-lg transition-colors ${
          isDarkMode
            ? 'bg-black'
            : 'bg-transparent'
        }`}>
          <div className="flex items-center justify-between mb-4">
            <h3 className={`text-lg font-semibold ${
              isDarkMode ? 'text-gray-100' : 'text-slate-800'
            }`}>Consultation Summary</h3>
            <div className="flex items-center gap-2">
              {hasContent && (
                <>
                  {/* Undo Button */}
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={undoLastChange}
                    disabled={undoStack.length === 0}
                    className={`transition-colors ${
                      isDarkMode
                        ? 'text-gray-300 hover:text-gray-200 disabled:text-gray-500'
                        : 'text-slate-600 hover:text-slate-700 disabled:text-slate-400'
                    }`}
                    title={undoStack.length > 0 ? `Undo: ${undoStack[undoStack.length - 1]?.action}` : 'No actions to undo'}
                  >
                    <Undo className="w-4 h-4 mr-1" />
                    Undo
                  </Button>

                  {/* Copy Button */}
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleCopy}
                    className={`transition-colors ${
                      isDarkMode
                        ? 'text-gray-300 hover:text-gray-200'
                        : 'text-slate-600 hover:text-slate-700'
                    }`}
                  >
                    {copySuccess ? (
                      <>
                        <Check className="w-4 h-4 mr-1 text-green-500" />
                        Copied
                      </>
                    ) : (
                      <>
                        <Copy className="w-4 h-4 mr-1" />
                        Copy
                      </>
                    )}
                  </Button>
                </>
              )}
            </div>
          </div>

          {isGenerating && (
            <div className="flex items-center justify-center py-8">
              <div className="text-center">
                <Loader2 className="w-8 h-8 animate-spin text-blue-600 mx-auto mb-3" />
                <p className={`text-sm ${
                  isDarkMode ? 'text-gray-300' : 'text-slate-600'
                }`}>Generating AI summary...</p>
              </div>
            </div>
          )}



          {hasContent && !isGenerating && (
            <JsonDrivenEditor
              ref={jsonEditorRef}
              consultationData={
                selectedConsultation?.edited_note_json ||
                selectedConsultation?.ai_generated_note_json ||
                {}
              }
              consultationType={selectedConsultation?.consultation_type || selectedTemplate}
              onUpdate={(updatedData) => {
                // Handle JSON updates - sync with parent component
                if (props.onConsultationUpdate && selectedConsultation) {
                  const updatedConsultation = {
                    ...selectedConsultation,
                    edited_note_json: updatedData
                  }
                  props.onConsultationUpdate(updatedConsultation)

                  // Also update Zustand store to keep it in sync
                  const updatedConsultationData = convertToConsultationData(selectedConsultation)
                  updatedConsultationData.edited_note_json = updatedData
                  setCurrentConsultation(updatedConsultationData)
                }
              }}

              readOnly={false}
            />
          )}
        </div>
      )}

      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        multiple
        onChange={(e) => e.target.files && handleImageUploadWithAutosave(e.target.files)}
        className="hidden"
      />
      </div>
    </Profiler>
  )
}