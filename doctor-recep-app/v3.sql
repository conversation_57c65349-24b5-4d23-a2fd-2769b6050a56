-- ====================================================================
-- CELER AI ARCHITECTURE 3.0 - MIGRATION SCRIPT (UP)
--
-- This script performs a safe, additive, in-place upgrade
-- of the existing database to support the new architecture.
-- It is designed to be non-blocking and reversible.
-- ====================================================================

BEGIN;

-- STEP 1: CREATE NEW, INDEPENDENT TABLES
-- These tables do not affect existing data or operations.

-- Table for tracking saga state (distributed transactions)
CREATE TABLE IF NOT EXISTS public.saga_state (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    saga_type TEXT NOT NULL,
    saga_id UUID NOT NULL UNIQUE,
    correlation_id UUID NOT NULL,
    current_step TEXT NOT NULL,
    completed_steps TEXT[] DEFAULT '{}',
    status TEXT NOT NULL DEFAULT 'running' CHECK (status IN ('running', 'completed', 'failed', 'compensating', 'compensated')),
    saga_data JSONB NOT NULL DEFAULT '{}',
    error_message TEXT,
    retry_count INTEGER DEFAULT 0,
    started_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    expires_at TIMESTAMPTZ
);

-- Table for tracking batch processing performance
CREATE TABLE IF NOT EXISTS public.batch_processing (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    batch_id UUID NOT NULL UNIQUE,
    batch_size INTEGER NOT NULL CHECK (batch_size > 0),
    consultation_ids UUID[] NOT NULL,
    status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'partial_success')),
    started_at TIMESTAMPTZ,
    completed_at TIMESTAMPTZ,
    successful_count INTEGER DEFAULT 0,
    failed_count INTEGER DEFAULT 0,
    worker_instance TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Table for immutable event sourcing (optional but good practice)
CREATE TABLE IF NOT EXISTS public.consultation_events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    consultation_id UUID NOT NULL,
    correlation_id UUID NOT NULL,
    event_type TEXT NOT NULL,
    event_data JSONB NOT NULL,
    aggregate_version INTEGER NOT NULL,
    timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Table for detailed auditing
CREATE TABLE IF NOT EXISTS public.consultation_audit (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    consultation_id UUID NOT NULL,
    correlation_id UUID,
    operation TEXT NOT NULL,
    operation_source TEXT NOT NULL,
    old_values JSONB,
    new_values JSONB,
    user_id UUID,
    timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW()
);


-- STEP 2: ADD NEW, NULLABLE COLUMNS TO THE EXISTING `consultations` TABLE
-- This is non-destructive and backwards-compatible.

ALTER TABLE public.consultations
    ADD COLUMN IF NOT EXISTS correlation_id UUID,
    ADD COLUMN IF NOT EXISTS version INTEGER DEFAULT 1,
    ADD COLUMN IF NOT EXISTS schema_version TEXT;


-- STEP 3: CREATE NEW PERFORMANCE INDEXES
-- Note: CONCURRENTLY removed due to Supabase transaction block limitations
-- These indexes will be created normally (with brief table locks)

CREATE INDEX IF NOT EXISTS idx_consultations_correlation_id ON public.consultations (correlation_id);
CREATE INDEX IF NOT EXISTS idx_saga_state_correlation_status ON public.saga_state (correlation_id, status);
CREATE INDEX IF NOT EXISTS idx_consultation_audit_consultation_timestamp ON public.consultation_audit (consultation_id, timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_consultation_events_consultation_version ON public.consultation_events (consultation_id, aggregate_version);


-- STEP 4: CREATE NEW STORED PROCEDURES
-- These will be used by the new Python worker.

-- Bulk Insert Function
CREATE OR REPLACE FUNCTION public.bulk_insert_consultations(consultations_json JSONB)
RETURNS TABLE(id UUID, correlation_id UUID) AS $$
DECLARE
    consultation_data JSONB;
BEGIN
    -- This temporary table will hold the data to be inserted
    CREATE TEMP TABLE temp_consultations_to_insert (
        id UUID,
        correlation_id UUID,
        user_id UUID,
        status TEXT,
        consultation_type TEXT,
        patient_name TEXT,
        primary_audio_url TEXT,
        image_urls JSONB,
        version INTEGER,
        schema_version TEXT
    ) ON COMMIT DROP;

    -- Unpack the incoming JSON array into the temporary table
    INSERT INTO temp_consultations_to_insert
    SELECT
        (c->>'id')::UUID,
        (c->>'correlation_id')::UUID,
        (c->>'doctorId')::UUID,
        'processing',
        (c->>'templateType')::TEXT,
        (c->>'patientName')::TEXT,
        (c->>'primaryAudioUrl')::TEXT,
        (c->>'imageUrls')::JSONB,
        1,
        '3.0'
    FROM jsonb_array_elements(consultations_json) c;

    -- Perform the bulk insert from the temporary table into the main table
    RETURN QUERY
    INSERT INTO public.consultations (
        id, doctor_id, status, consultation_type, patient_name, primary_audio_url, image_urls,
        correlation_id, version, schema_version, updated_at, created_at
    )
    SELECT
        ti.id, ti.user_id, ti.status::public.consultation_status, ti.consultation_type::public.consultation_type,
        ti.patient_name, ti.primary_audio_url, ti.image_urls,
        ti.correlation_id, ti.version, ti.schema_version, NOW(), NOW()
    FROM temp_consultations_to_insert ti
    -- Avoid inserting duplicates based on correlation_id
    ON CONFLICT (correlation_id) DO NOTHING
    RETURNING consultations.id, consultations.correlation_id;

END;
$$ LANGUAGE plpgsql;


-- Bulk Update Function
CREATE OR REPLACE FUNCTION public.bulk_update_summaries(updates_json JSONB)
RETURNS TABLE(updated_id UUID, new_version INTEGER) AS $$
BEGIN
    -- This temporary table will hold the updates
    CREATE TEMP TABLE temp_updates (
        id UUID,
        summary JSONB,
        status TEXT
    ) ON COMMIT DROP;

    -- Unpack the incoming JSON array into the temporary table
    INSERT INTO temp_updates
    SELECT
        (u->>'id')::UUID,
        (u->>'summary')::JSONB,
        (u->>'status')::TEXT
    FROM jsonb_array_elements(updates_json) u;

    -- Perform the bulk update by joining with the temporary table
    RETURN QUERY
    UPDATE public.consultations c
    SET
        ai_generated_note_json = tu.summary,
        status = tu.status::public.consultation_status,
        version = c.version + 1,
        updated_at = NOW()
    FROM temp_updates tu
    WHERE c.id = tu.id
    RETURNING c.id, c.version;

END;
$$ LANGUAGE plpgsql;

COMMIT;

-- ====================================================================
-- MIGRATION UP SCRIPT COMPLETE
-- ====================================================================```

---
### ❌ **Script 2: `migration_down.sql`**
*This script REVERTS all changes made by the UP script. Use it for emergency rollbacks.*

```sql
-- ====================================================================
-- CELER AI ARCHITECTURE 3.0 - MIGRATION SCRIPT (DOWN)
--
-- This script safely reverts all changes made by the UP script.
-- It is designed for emergency rollbacks.
-- ====================================================================

BEGIN;

-- STEP 1: DROP NEW STORED PROCEDURES

DROP FUNCTION IF EXISTS public.bulk_insert_consultations(JSONB);
DROP FUNCTION IF EXISTS public.bulk_update_summaries(JSONB);


-- STEP 2: DROP NEW PERFORMANCE INDEXES

DROP INDEX IF EXISTS public.idx_consultations_correlation_id;
DROP INDEX IF EXISTS public.idx_saga_state_correlation_status;
DROP INDEX IF EXISTS public.idx_consultation_audit_consultation_timestamp;
DROP INDEX IF EXISTS public.idx_consultation_events_consultation_version;


-- STEP 3: DROP NEW COLUMNS FROM THE `consultations` TABLE

ALTER TABLE public.consultations
    DROP COLUMN IF EXISTS correlation_id,
    DROP COLUMN IF EXISTS version,
    DROP COLUMN IF EXISTS schema_version;


-- STEP 4: DROP NEW, INDEPENDENT TABLES

DROP TABLE IF EXISTS public.consultation_audit;
DROP TABLE IF EXISTS public.consultation_events;
DROP TABLE IF EXISTS public.batch_processing;
DROP TABLE IF EXISTS public.saga_state;

COMMIT;

-- ====================================================================
-- MIGRATION DOWN SCRIPT COMPLETE
-- ====================================================================